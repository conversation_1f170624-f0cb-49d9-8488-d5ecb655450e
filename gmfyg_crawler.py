#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国工艺美术馆 中国非物质文化遗产馆 典藏图片爬虫
功能：遍历所有分类的分页数据，下载缩略图并生成汇总表格
"""

import os
import re
import time
import requests
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
import pandas as pd
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("crawler.log", encoding="utf-8"),
        logging.StreamHandler(),
    ],
)


class GMFYGCrawler:
    def __init__(self, base_dir="images"):
        self.base_url = "https://www.gmfyg.org.cn"
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

        # 分类信息
        self.categories = {
            "01": "玉雕",
            "02": "石雕",
            "03": "金属工艺",
            "04": "陶瓷",
            "05": "竹木雕",
            "06": "民族民间工艺",
            "07": "牙角骨雕",
            "08": "玻璃",
            "09": "织染绣",
            "10": "唐卡",
            "11": "漆器漆艺",
            "12": "工艺家具",
        }

        # 请求会话
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "zh-CN,zh;q=0.8,en-US;q=0.5,en;q=0.3",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
        )

        # 统计信息
        self.stats = {}

    def get_page_content(self, url, retries=3):
        """获取页面内容"""
        for attempt in range(retries):
            try:
                response = self.session.get(url, timeout=30)
                response.raise_for_status()
                response.encoding = "utf-8"
                return response.text
            except Exception as e:
                logging.warning(
                    f"获取页面失败 (尝试 {attempt + 1}/{retries}): {url}, 错误: {e}"
                )
                if attempt < retries - 1:
                    time.sleep(2**attempt)  # 指数退避
                else:
                    logging.error(f"获取页面最终失败: {url}")
                    return None

    def get_total_pages(self, category_id):
        """获取分类的总页数"""
        url = f"{self.base_url}/collection/list?category={category_id}"
        content = self.get_page_content(url)
        if not content:
            return 1

        soup = BeautifulSoup(content, "html.parser")

        # 查找分页信息 - 寻找包含页码链接的区域
        page_links = soup.find_all("a", href=True)
        max_page = 1

        for link in page_links:
            href = link.get("href", "")
            # 匹配页码模式: /collection/list/数字?category=分类号
            page_match = re.search(
                r"/collection/list/(\d+)\?category=" + category_id, href
            )
            if page_match:
                page_num = int(page_match.group(1))
                max_page = max(max_page, page_num)

        # 如果没有找到分页链接，检查是否有数字链接
        if max_page == 1:
            for link in page_links:
                text = link.get_text(strip=True)
                href = link.get("href", "")
                # 检查纯数字链接
                if text.isdigit() and f"category={category_id}" in href:
                    page_num = int(text)
                    max_page = max(max_page, page_num)

        return max_page

    def extract_images_from_page(self, content):
        """从页面内容中提取图片URL"""
        if not content:
            return []

        soup = BeautifulSoup(content, "html.parser")
        images = []

        # 查找所有img标签
        img_tags = soup.find_all("img")

        for img in img_tags:
            src = img.get("src", "")
            if src.startswith("https://file.gmfyg.org.cn/collection/WaterMarks/图片/"):
                images.append(src)

        return images

    def download_image(self, img_url, save_path):
        """下载单张图片"""
        try:
            if save_path.exists():
                logging.debug(f"图片已存在，跳过: {save_path}")
                return True

            response = self.session.get(img_url, timeout=30)
            response.raise_for_status()

            save_path.parent.mkdir(parents=True, exist_ok=True)
            with open(save_path, "wb") as f:
                f.write(response.content)

            logging.debug(f"下载成功: {save_path}")
            return True

        except Exception as e:
            logging.error(f"下载图片失败: {img_url}, 错误: {e}")
            return False

    def crawl_category(self, category_id):
        """爬取指定分类的所有图片"""
        category_name = self.categories[category_id]
        logging.info(f"开始爬取分类: {category_id} - {category_name}")

        # 创建分类目录
        category_dir = self.base_dir / f"{category_id}_{category_name}"
        category_dir.mkdir(exist_ok=True)

        # 获取总页数
        total_pages = self.get_total_pages(category_id)
        logging.info(f"分类 {category_id} 共 {total_pages} 页")

        all_images = []
        downloaded_count = 0

        # 遍历所有页面
        for page in range(1, total_pages + 1):
            logging.info(f"正在处理分类 {category_id} 第 {page}/{total_pages} 页")

            # 构建页面URL
            if page == 1:
                url = f"{self.base_url}/collection/list?category={category_id}"
            else:
                url = f"{self.base_url}/collection/list/{page}?category={category_id}"

            # 获取页面内容
            content = self.get_page_content(url)
            if not content:
                continue

            # 提取图片URL
            page_images = self.extract_images_from_page(content)
            all_images.extend(page_images)

            # 下载图片
            for img_url in page_images:
                # 从URL中提取文件名
                parsed_url = urlparse(img_url)
                filename = os.path.basename(parsed_url.path)
                if not filename:
                    filename = f"image_{len(all_images)}.jpg"

                save_path = category_dir / filename

                if self.download_image(img_url, save_path):
                    downloaded_count += 1

                # 添加延迟避免请求过快
                time.sleep(0.5)

        # 记录统计信息
        self.stats[category_id] = {
            "name": category_name,
            "total_pages": total_pages,
            "total_images": len(all_images),
            "downloaded_images": downloaded_count,
        }

        logging.info(
            f"分类 {category_id} 完成: 共 {total_pages} 页, {len(all_images)} 张图片, 成功下载 {downloaded_count} 张"
        )

    def crawl_all_categories(self):
        """爬取所有分类"""
        logging.info("开始爬取所有分类...")

        for category_id in self.categories.keys():
            try:
                self.crawl_category(category_id)
                # 分类间添加延迟
                time.sleep(2)
            except Exception as e:
                logging.error(f"爬取分类 {category_id} 时出错: {e}")
                continue

        logging.info("所有分类爬取完成")

    def generate_summary_table(self):
        """生成汇总表格"""
        logging.info("生成汇总表格...")

        # 准备数据
        data = []
        for category_id, stats in self.stats.items():
            data.append(
                {
                    "分类号": category_id,
                    "分类名称": stats["name"],
                    "共X页": stats["total_pages"],
                    "图片数量": stats["total_images"],
                    "下载成功": stats["downloaded_images"],
                }
            )

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 保存为CSV
        csv_path = self.base_dir / "汇总表格.csv"
        df.to_csv(csv_path, index=False, encoding="utf-8-sig")

        # 保存为Markdown
        md_path = self.base_dir / "汇总表格.md"
        with open(md_path, "w", encoding="utf-8") as f:
            f.write("# 中国工艺美术馆典藏图片爬取汇总\n\n")
            f.write(df.to_markdown(index=False))
            f.write(f"\n\n**总计**: {df['图片数量'].sum()} 张图片")

        logging.info(f"汇总表格已保存: {csv_path} 和 {md_path}")

        # 打印汇总信息
        print("\n" + "=" * 50)
        print("爬取汇总:")
        print("=" * 50)
        print(df.to_string(index=False))
        print(f"\n总计: {df['图片数量'].sum()} 张图片")
        print("=" * 50)


def main():
    """主函数"""
    crawler = GMFYGCrawler()

    try:
        # 爬取所有分类
        crawler.crawl_all_categories()

        # 生成汇总表格
        crawler.generate_summary_table()

    except KeyboardInterrupt:
        logging.info("用户中断程序")
    except Exception as e:
        logging.error(f"程序执行出错: {e}")
    finally:
        logging.info("程序结束")


if __name__ == "__main__":
    main()
