@echo off
chcp 65001 >nul
echo ========================================
echo 中国工艺美术馆典藏图片爬虫
echo ========================================
echo.

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境，请先安装Python
    pause
    exit /b 1
)

echo 检查依赖包...
pip show requests >nul 2>&1
if errorlevel 1 (
    echo 安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo.
echo 请选择运行模式:
echo 1. 测试模式 (仅爬取每个分类第一页)
echo 2. 完整模式 (爬取所有分页数据)
echo.
set /p choice=请输入选择 (1 或 2): 

if "%choice%"=="1" (
    echo.
    echo 运行测试模式...
    python test_crawler.py
) else if "%choice%"=="2" (
    echo.
    echo 运行完整模式...
    echo 警告: 完整模式可能需要较长时间，请确保网络稳定
    echo.
    set /p confirm=确认继续? (y/n): 
    if /i "%confirm%"=="y" (
        python gmfyg_crawler.py
    ) else (
        echo 已取消运行
    )
) else (
    echo 无效选择
)

echo.
echo 程序结束
pause
