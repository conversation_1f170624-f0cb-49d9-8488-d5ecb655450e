#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
根据Excel文件中的总登记号，将匹配的照片复制到"空图"目录
"""

import os
import shutil
import pandas as pd
from pathlib import Path

def read_excel_numbers(excel_path):
    """读取Excel文件第一列的总登记号"""
    try:
        # 尝试读取Excel文件
        df = pd.read_excel(excel_path)
        
        # 获取第一列的数据
        first_column = df.iloc[:, 0]
        
        # 转换为字符串并去除空值
        numbers = []
        for value in first_column:
            if pd.notna(value):  # 排除NaN值
                # 转换为字符串并去除空格
                str_value = str(value).strip()
                if str_value:  # 排除空字符串
                    numbers.append(str_value)
        
        return numbers
        
    except Exception as e:
        print(f"读取Excel文件失败: {e}")
        return []

def find_matching_images(numbers, all_dir):
    """在All目录中查找匹配的图片文件"""
    all_path = Path(all_dir)
    if not all_path.exists():
        print(f"错误: {all_dir} 目录不存在")
        return []
    
    matching_files = []
    not_found = []
    
    for number in numbers:
        # 尝试多种可能的文件名格式
        possible_names = [
            f"{number}.jpg",
            f"{number}.jpeg",
            f"{number}.png",
            f"{number}.JPG",
            f"{number}.JPEG",
            f"{number}.PNG"
        ]
        
        found = False
        for name in possible_names:
            file_path = all_path / name
            if file_path.exists():
                matching_files.append((number, file_path))
                found = True
                break
        
        if not found:
            not_found.append(number)
    
    return matching_files, not_found

def copy_images_to_empty_dir(matching_files, target_dir):
    """将匹配的图片复制到空图目录"""
    target_path = Path(target_dir)
    target_path.mkdir(exist_ok=True)
    
    copied_count = 0
    failed_count = 0
    
    print(f"开始复制图片到 {target_dir} 目录...")
    print("=" * 50)
    
    for number, source_file in matching_files:
        try:
            # 保持原始文件名
            target_file = target_path / source_file.name
            
            # 如果目标文件已存在，跳过
            if target_file.exists():
                print(f"跳过已存在: {source_file.name}")
                continue
            
            # 复制文件
            shutil.copy2(source_file, target_file)
            print(f"复制成功: {number} -> {source_file.name}")
            copied_count += 1
            
        except Exception as e:
            print(f"复制失败: {number} - {e}")
            failed_count += 1
    
    return copied_count, failed_count

def main():
    """主函数"""
    # 文件路径配置
    excel_path = r"c:\Users\<USER>\Documents\augment-projects\demo\空图编号表.xls"
    all_dir = "All"
    target_dir = "空图"
    
    print("开始处理空图编号表...")
    print("=" * 50)
    
    # 检查Excel文件是否存在
    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在: {excel_path}")
        return
    
    # 读取Excel文件中的编号
    print("正在读取Excel文件...")
    numbers = read_excel_numbers(excel_path)
    
    if not numbers:
        print("错误: 未能从Excel文件中读取到有效的编号")
        return
    
    print(f"成功读取 {len(numbers)} 个编号")
    print(f"前10个编号示例: {numbers[:10]}")
    print()
    
    # 查找匹配的图片文件
    print("正在查找匹配的图片文件...")
    matching_files, not_found = find_matching_images(numbers, all_dir)
    
    print(f"找到匹配图片: {len(matching_files)} 张")
    print(f"未找到图片: {len(not_found)} 个编号")
    
    if not_found:
        print(f"未找到的编号示例: {not_found[:10]}")
    print()
    
    # 复制匹配的图片
    if matching_files:
        copied_count, failed_count = copy_images_to_empty_dir(matching_files, target_dir)
        
        print("=" * 50)
        print("处理完成!")
        print(f"成功复制: {copied_count} 张图片")
        print(f"复制失败: {failed_count} 张图片")
        print(f"目标目录: {Path(target_dir).absolute()}")
        
        # 生成未找到编号的报告
        if not_found:
            report_file = Path(target_dir) / "未找到的编号.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("未找到对应图片的编号列表\n")
                f.write("=" * 30 + "\n")
                for number in not_found:
                    f.write(f"{number}\n")
            print(f"未找到编号列表已保存到: {report_file}")
    else:
        print("没有找到任何匹配的图片文件")

if __name__ == "__main__":
    main()
