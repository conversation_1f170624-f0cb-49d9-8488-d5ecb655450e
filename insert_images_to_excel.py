#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将匹配的图片插入到Excel表格的第四列
"""

import os
import pandas as pd
from pathlib import Path
from openpyxl import load_workbook
from openpyxl.drawing.image import Image
from openpyxl.utils.dataframe import dataframe_to_rows
import warnings

warnings.filterwarnings("ignore")


def resize_image(image_path, max_width=150, max_height=150):
    """调整图片大小以适合Excel单元格"""
    try:
        from PIL import Image as PILImage

        # 设置PIL的最大图片像素限制
        PILImage.MAX_IMAGE_PIXELS = None

        # 打开图片
        with PILImage.open(image_path) as img:
            # 获取原始尺寸
            width, height = img.size

            # 如果图片太大，先进行预缩放
            if width * height > 50000000:  # 5000万像素
                scale_factor = (50000000 / (width * height)) ** 0.5
                width = int(width * scale_factor)
                height = int(height * scale_factor)

            # 计算缩放比例
            width_ratio = max_width / width
            height_ratio = max_height / height
            ratio = min(width_ratio, height_ratio)

            # 计算新尺寸
            new_width = int(width * ratio)
            new_height = int(height * ratio)

            return new_width, new_height
    except ImportError:
        # 如果没有PIL，使用默认尺寸
        return max_width, max_height
    except Exception as e:
        print(f"图片处理警告 {image_path}: {e}")
        return max_width, max_height


def insert_images_to_excel():
    """将图片插入到Excel表格"""
    # 文件路径配置
    excel_path = (
        r"c:\Users\<USER>\Documents\augment-projects\demo\空图编号表.xls"
    )
    images_dir = "空图"
    output_path = "空图编号表_带图片.xlsx"

    print("开始处理Excel文件和图片...")
    print("=" * 50)

    # 检查文件和目录是否存在
    if not os.path.exists(excel_path):
        print(f"错误: Excel文件不存在: {excel_path}")
        return

    images_path = Path(images_dir)
    if not images_path.exists():
        print(f"错误: 图片目录不存在: {images_dir}")
        return

    try:
        # 读取Excel文件
        print("正在读取Excel文件...")
        df = pd.read_excel(excel_path)
        print(f"Excel文件包含 {len(df)} 行数据")
        print(f"列名: {list(df.columns)}")

        # 确保有足够的列
        while len(df.columns) < 4:
            df[f"列{len(df.columns)+1}"] = ""

        # 重命名列以便识别
        if len(df.columns) >= 4:
            df.columns = list(df.columns[:3]) + ["图片"] + list(df.columns[4:])

        # 创建新的Excel工作簿
        print("正在创建新的Excel文件...")
        df.to_excel(output_path, index=False, engine="openpyxl")

        # 使用openpyxl插入图片
        print("正在插入图片...")
        wb = load_workbook(output_path)
        ws = wb.active

        # 设置行高和列宽
        ws.column_dimensions["D"].width = 20  # 第四列宽度

        inserted_count = 0
        not_found_count = 0

        # 遍历每一行数据
        for row_idx, row in df.iterrows():
            excel_row = row_idx + 2  # Excel行号（从2开始，因为第1行是标题）

            # 获取第一列的编号
            number = str(row.iloc[0]).strip() if pd.notna(row.iloc[0]) else ""

            if not number:
                continue

            # 查找对应的图片文件
            image_file = images_path / f"{number}.jpg"

            if image_file.exists():
                try:
                    # 设置行高
                    ws.row_dimensions[excel_row].height = 120

                    # 创建图片对象
                    img = Image(str(image_file))

                    # 调整图片大小
                    width, height = resize_image(image_file, 150, 120)
                    img.width = width
                    img.height = height

                    # 插入图片到第四列
                    cell = f"D{excel_row}"
                    ws.add_image(img, cell)

                    inserted_count += 1
                    if inserted_count % 50 == 0:
                        print(f"已插入 {inserted_count} 张图片...")

                except Exception as e:
                    print(f"插入图片失败 {number}: {e}")
                    not_found_count += 1
            else:
                not_found_count += 1

        # 保存文件
        print("正在保存文件...")
        wb.save(output_path)
        wb.close()

        print("=" * 50)
        print("处理完成!")
        print(f"成功插入图片: {inserted_count} 张")
        print(f"未找到图片: {not_found_count} 个")
        print(f"输出文件: {Path(output_path).absolute()}")

        # 生成处理报告
        print(f"\n文件大小: {os.path.getsize(output_path) / 1024 / 1024:.2f} MB")
        print("注意: 带图片的Excel文件可能较大，请耐心等待打开")

    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        import traceback

        traceback.print_exc()


def install_pillow():
    """安装PIL库用于图片处理"""
    try:
        import PIL

        print("PIL库已安装")
        return True
    except ImportError:
        print("正在安装PIL库...")
        import subprocess

        try:
            subprocess.check_call(["pip", "install", "Pillow"])
            print("PIL库安装成功")
            return True
        except Exception as e:
            print(f"PIL库安装失败: {e}")
            print("将使用默认图片尺寸")
            return False


if __name__ == "__main__":
    # 尝试安装PIL库
    install_pillow()

    # 执行主函数
    insert_images_to_excel()
