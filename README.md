# 中国工艺美术馆典藏图片爬虫

这是一个用于爬取中国工艺美术馆 中国非物质文化遗产馆典藏图片的Python爬虫程序。

## 功能特性

- 🎯 **全面覆盖**: 遍历所有12个分类的完整分页数据
- 📁 **分类存储**: 按分类目录组织下载的图片
- 📊 **统计汇总**: 生成详细的爬取统计表格
- 🔄 **断点续传**: 支持重复运行，已下载的图片会自动跳过
- 📝 **详细日志**: 完整的运行日志记录
- ⚡ **智能重试**: 网络异常时自动重试机制

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本使用

```bash
python gmfyg_crawler.py
```

### 自定义图片保存目录

```python
from gmfyg_crawler import GMFYGCrawler

# 指定自定义目录
crawler = GMFYGCrawler(base_dir="my_images")
crawler.crawl_all_categories()
crawler.generate_summary_table()
```

## 输出结构

程序运行后会创建以下目录结构：

```
images/
├── 01_玉雕/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── 02_石雕/
│   ├── image1.jpg
│   └── ...
├── 03_金属工艺/
├── 04_陶瓷/
├── 05_竹木雕/
├── 06_民族民间工艺/
├── 07_牙角骨雕/
├── 08_玻璃/
├── 09_织染绣/
├── 10_唐卡/
├── 11_漆器漆艺/
├── 12_工艺家具/
├── 汇总表格.csv
├── 汇总表格.md
└── crawler.log
```

## 汇总表格格式

| 分类号 | 分类名称 | 共X页 | 图片数量 | 下载成功 |
|--------|----------|-------|----------|----------|
| 01 | 玉雕 | 8 | 160 | 160 |
| 02 | 石雕 | 17 | 340 | 340 |
| ... | ... | ... | ... | ... |

## 配置说明

### 请求头设置
程序使用了标准的浏览器请求头，避免被反爬虫机制拦截。

### 延迟设置
- 图片下载间隔：0.5秒
- 分类切换间隔：2秒
- 重试延迟：指数退避策略

### 日志级别
- INFO: 主要进度信息
- DEBUG: 详细调试信息
- ERROR: 错误信息
- WARNING: 警告信息

## 注意事项

1. **网络稳定性**: 建议在网络稳定的环境下运行
2. **存储空间**: 确保有足够的磁盘空间存储图片
3. **运行时间**: 完整爬取可能需要较长时间，建议耐心等待
4. **合规使用**: 请遵守网站的robots.txt和使用条款
5. **适度请求**: 程序已内置延迟机制，请勿修改为过于频繁的请求

## 错误处理

程序具备完善的错误处理机制：

- **网络超时**: 自动重试3次
- **HTTP错误**: 记录错误并继续处理其他图片
- **文件写入错误**: 跳过当前文件，继续下载
- **解析错误**: 记录错误页面，继续处理

## 技术实现

- **HTTP客户端**: requests + Session复用
- **HTML解析**: BeautifulSoup4
- **数据处理**: pandas
- **路径处理**: pathlib
- **日志系统**: logging

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站使用条款。

## 更新日志

### v1.0.0 (2025-08-01)
- 初始版本发布
- 支持12个分类的完整爬取
- 实现分类目录存储
- 添加统计汇总功能
