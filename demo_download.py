#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示下载功能 - 下载每个分类的前3张图片
"""

import os
import requests
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from pathlib import Path
import time

def download_demo():
    """演示下载功能"""
    base_url = "https://www.gmfyg.org.cn"
    demo_dir = Path("demo_images")
    demo_dir.mkdir(exist_ok=True)
    
    categories = {
        "01": "玉雕",
        "02": "石雕", 
        "03": "金属工艺"
    }
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    })
    
    total_downloaded = 0
    
    for category_id, category_name in categories.items():
        print(f"正在处理分类: {category_id} - {category_name}")
        
        # 创建分类目录
        category_dir = demo_dir / f"{category_id}_{category_name}"
        category_dir.mkdir(exist_ok=True)
        
        # 获取第一页
        url = f"{base_url}/collection/list?category={category_id}"
        
        try:
            response = session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            
            soup = BeautifulSoup(response.text, 'html.parser')
            img_tags = soup.find_all('img')
            
            images = []
            for img in img_tags:
                src = img.get('src', '')
                if src.startswith('https://file.gmfyg.org.cn/collection/WaterMarks/图片/'):
                    images.append(src)
            
            # 下载前3张图片
            for i, img_url in enumerate(images[:3]):
                print(f"  下载图片 {i+1}: {img_url}")
                
                try:
                    # 获取文件名
                    parsed_url = urlparse(img_url)
                    filename = os.path.basename(parsed_url.path)
                    if not filename:
                        filename = f"image_{i+1}.jpg"
                    
                    save_path = category_dir / filename
                    
                    # 下载图片
                    img_response = session.get(img_url, timeout=30)
                    img_response.raise_for_status()
                    
                    with open(save_path, 'wb') as f:
                        f.write(img_response.content)
                    
                    print(f"    保存成功: {save_path}")
                    total_downloaded += 1
                    
                except Exception as e:
                    print(f"    下载失败: {e}")
                
                time.sleep(1)  # 延迟1秒
                
        except Exception as e:
            print(f"  获取页面失败: {e}")
            continue
        
        time.sleep(2)  # 分类间延迟
    
    print(f"\n演示完成！共下载 {total_downloaded} 张图片到 {demo_dir} 目录")
    print("如果下载成功，可以运行完整版爬虫：python gmfyg_crawler.py")

if __name__ == "__main__":
    download_demo()
