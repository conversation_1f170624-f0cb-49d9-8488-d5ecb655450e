#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试版爬虫 - 仅爬取每个分类的第一页进行测试
"""

import os
import re
import time
import requests
from urllib.parse import urlparse
from bs4 import BeautifulSoup
import pandas as pd
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)


class TestCrawler:
    def __init__(self, base_dir="test_images"):
        self.base_url = "https://www.gmfyg.org.cn"
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

        # 分类信息
        self.categories = {
            "01": "玉雕",
            "02": "石雕",
            "03": "金属工艺",
            "04": "陶瓷",
            "05": "竹木雕",
            "06": "民族民间工艺",
            "07": "牙角骨雕",
            "08": "玻璃",
            "09": "织染绣",
            "10": "唐卡",
            "11": "漆器漆艺",
            "12": "工艺家具",
        }

        # 请求会话
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
            }
        )

        self.stats = {}

    def get_page_content(self, url):
        """获取页面内容"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = "utf-8"
            return response.text
        except Exception as e:
            logging.error(f"获取页面失败: {url}, 错误: {e}")
            return None

    def get_total_pages(self, category_id):
        """获取分类的总页数"""
        url = f"{self.base_url}/collection/list?category={category_id}"
        content = self.get_page_content(url)
        if not content:
            return 1

        soup = BeautifulSoup(content, "html.parser")

        # 查找分页信息 - 寻找包含页码链接的区域
        page_links = soup.find_all("a", href=True)
        max_page = 1

        for link in page_links:
            href = link.get("href", "")
            # 匹配页码模式: /collection/list/数字?category=分类号
            page_match = re.search(
                r"/collection/list/(\d+)\?category=" + category_id, href
            )
            if page_match:
                page_num = int(page_match.group(1))
                max_page = max(max_page, page_num)

        # 如果没有找到分页链接，检查是否有数字链接
        if max_page == 1:
            for link in page_links:
                text = link.get_text(strip=True)
                href = link.get("href", "")
                # 检查纯数字链接
                if text.isdigit() and f"category={category_id}" in href:
                    page_num = int(text)
                    max_page = max(max_page, page_num)

        return max_page

    def extract_images_from_page(self, content):
        """从页面内容中提取图片URL"""
        if not content:
            return []

        soup = BeautifulSoup(content, "html.parser")
        images = []

        img_tags = soup.find_all("img")

        for img in img_tags:
            src = img.get("src", "")
            if src.startswith("https://file.gmfyg.org.cn/collection/WaterMarks/图片/"):
                images.append(src)

        return images

    def test_category(self, category_id):
        """测试单个分类（仅第一页）"""
        category_name = self.categories[category_id]
        logging.info(f"测试分类: {category_id} - {category_name}")

        # 获取总页数
        total_pages = self.get_total_pages(category_id)

        # 获取第一页内容
        url = f"{self.base_url}/collection/list?category={category_id}"
        content = self.get_page_content(url)

        if content:
            images = self.extract_images_from_page(content)
            logging.info(
                f"分类 {category_id} - 总页数: {total_pages}, 第一页图片数: {len(images)}"
            )

            # 显示前3个图片URL作为示例
            for i, img_url in enumerate(images[:3]):
                logging.info(f"  示例图片 {i+1}: {img_url}")
        else:
            images = []
            logging.error(f"无法获取分类 {category_id} 的内容")

        self.stats[category_id] = {
            "name": category_name,
            "total_pages": total_pages,
            "first_page_images": len(images),
        }

        time.sleep(1)  # 延迟1秒

    def test_all_categories(self):
        """测试所有分类"""
        logging.info("开始测试所有分类...")

        for category_id in self.categories.keys():
            try:
                self.test_category(category_id)
            except Exception as e:
                logging.error(f"测试分类 {category_id} 时出错: {e}")
                continue

        logging.info("测试完成")

    def generate_test_report(self):
        """生成测试报告"""
        logging.info("生成测试报告...")

        data = []
        for category_id, stats in self.stats.items():
            data.append(
                {
                    "分类号": category_id,
                    "分类名称": stats["name"],
                    "总页数": stats["total_pages"],
                    "第一页图片数": stats["first_page_images"],
                    "预估总图片数": stats["total_pages"] * stats["first_page_images"],
                }
            )

        df = pd.DataFrame(data)

        # 保存测试报告
        report_path = self.base_dir / "测试报告.csv"
        df.to_csv(report_path, index=False, encoding="utf-8-sig")

        md_path = self.base_dir / "测试报告.md"
        with open(md_path, "w", encoding="utf-8") as f:
            f.write("# 爬虫测试报告\n\n")
            f.write(df.to_markdown(index=False))
            f.write(f"\n\n**预估总图片数**: {df['预估总图片数'].sum()} 张")

        print("\n" + "=" * 60)
        print("测试报告:")
        print("=" * 60)
        print(df.to_string(index=False))
        print(f"\n预估总图片数: {df['预估总图片数'].sum()} 张")
        print("=" * 60)


def main():
    """主函数"""
    crawler = TestCrawler()

    try:
        crawler.test_all_categories()
        crawler.generate_test_report()

        print("\n测试完成！如果测试结果正常，可以运行完整版爬虫：")
        print("python gmfyg_crawler.py")

    except KeyboardInterrupt:
        logging.info("用户中断测试")
    except Exception as e:
        logging.error(f"测试出错: {e}")


if __name__ == "__main__":
    main()
