#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将所有分类的图片复制到All目录，并去掉文件名中的中文字符
"""

import os
import shutil
import re
from pathlib import Path

def is_chinese_char(char):
    """判断字符是否为中文字符"""
    return '\u4e00' <= char <= '\u9fff'

def clean_filename(filename):
    """
    清理文件名：遇到第一个中文字符后，后面的内容都删除，但保留扩展名
    """
    # 分离文件名和扩展名
    name, ext = os.path.splitext(filename)
    
    # 查找第一个中文字符的位置
    first_chinese_pos = -1
    for i, char in enumerate(name):
        if is_chinese_char(char):
            first_chinese_pos = i
            break
    
    # 如果找到中文字符，截取到第一个中文字符之前
    if first_chinese_pos != -1:
        name = name[:first_chinese_pos]
    
    # 去掉末尾的空格和特殊字符
    name = name.rstrip(' -_.')
    
    # 如果处理后文件名为空，使用默认名称
    if not name:
        name = "image"
    
    return name + ext

def copy_images_to_all():
    """将所有图片复制到All目录"""
    images_dir = Path("images")
    all_dir = Path("All")
    
    # 创建All目录
    all_dir.mkdir(exist_ok=True)
    
    if not images_dir.exists():
        print("错误: images目录不存在")
        return
    
    total_copied = 0
    total_skipped = 0
    name_conflicts = {}  # 记录重名文件
    
    print("开始复制图片到All目录...")
    print("=" * 50)
    
    # 遍历所有分类目录
    for category_dir in images_dir.iterdir():
        if not category_dir.is_dir():
            continue
            
        category_name = category_dir.name
        print(f"处理分类: {category_name}")
        
        category_count = 0
        
        # 遍历分类目录中的所有图片
        for image_file in category_dir.iterdir():
            if not image_file.is_file():
                continue
                
            # 检查是否为图片文件
            if not image_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
                continue
            
            # 清理文件名
            original_name = image_file.name
            clean_name = clean_filename(original_name)
            
            # 处理重名文件
            target_path = all_dir / clean_name
            counter = 1
            base_name, ext = os.path.splitext(clean_name)
            
            while target_path.exists():
                # 如果文件内容相同，跳过
                if target_path.stat().st_size == image_file.stat().st_size:
                    print(f"  跳过重复文件: {original_name} -> {clean_name}")
                    total_skipped += 1
                    break
                
                # 文件名冲突，添加序号
                new_name = f"{base_name}_{counter}{ext}"
                target_path = all_dir / new_name
                counter += 1
            else:
                # 复制文件
                try:
                    shutil.copy2(image_file, target_path)
                    print(f"  复制: {original_name} -> {target_path.name}")
                    
                    # 记录文件名变化
                    if original_name != target_path.name:
                        if original_name not in name_conflicts:
                            name_conflicts[original_name] = target_path.name
                    
                    total_copied += 1
                    category_count += 1
                    
                except Exception as e:
                    print(f"  错误: 复制 {original_name} 失败 - {e}")
        
        print(f"  {category_name}: 复制了 {category_count} 张图片")
        print()
    
    print("=" * 50)
    print("复制完成!")
    print(f"总计复制: {total_copied} 张图片")
    print(f"跳过重复: {total_skipped} 张图片")
    print(f"目标目录: {all_dir.absolute()}")
    
    # 保存文件名映射记录
    if name_conflicts:
        mapping_file = all_dir / "文件名映射.txt"
        with open(mapping_file, 'w', encoding='utf-8') as f:
            f.write("原始文件名 -> 新文件名\n")
            f.write("=" * 50 + "\n")
            for original, new in sorted(name_conflicts.items()):
                f.write(f"{original} -> {new}\n")
        print(f"文件名映射记录已保存到: {mapping_file}")
    
    print("\n示例文件名处理:")
    print("原始: 1403 北京 柳朝国 吉祥如意福寿薄胎瓶.jpg")
    print("处理后: 1403.jpg")
    print("原始: 2334.jpg")
    print("处理后: 2334.jpg")

if __name__ == "__main__":
    copy_images_to_all()
